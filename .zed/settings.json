{"languages": {"JavaScript": {"formatter": {"language_server": {"name": "biome"}}, "code_actions_on_format": {"source.fixAll.biome": true, "source.organizeImports.biome": true}}, "TypeScript": {"formatter": {"language_server": {"name": "biome"}}, "code_actions_on_format": {"source.fixAll.biome": true, "source.organizeImports.biome": true}}, "TSX": {"formatter": {"language_server": {"name": "biome"}}, "code_actions_on_format": {"source.fixAll.biome": true, "source.organizeImports.biome": true}}, "JSON": {"formatter": {"language_server": {"name": "biome"}}, "code_actions_on_format": {"source.fixAll.biome": true, "source.organizeImports.biome": true}}, "JSONC": {"formatter": {"language_server": {"name": "biome"}}, "code_actions_on_format": {"source.fixAll.biome": true, "source.organizeImports.biome": true}}}}