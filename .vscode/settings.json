{"[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[json][jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "[astro]": {"editor.defaultFormatter": "astro-build.astro-vscode"}, "[markdown]": {"editor.wordWrap": "on"}, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.biome": "explicit", "source.organizeImports.biome": "explicit"}, "typescript.tsdk": "node_modules/typescript/lib", "astro.content-intellisense": true, "files.associations": {"*.css": "tailwindcss"}, "tailwindCSS.experimental.configFile": "./src/styles/global.css"}