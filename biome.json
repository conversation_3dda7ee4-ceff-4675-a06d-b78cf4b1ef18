{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "files": {"includes": ["**", "!**/build", "!**/dist", "!**/node_modules", "!**/.vercel", "!**/.netlify", "!**/.vscode", "!**/.astro"]}, "formatter": {"enabled": true, "indentStyle": "tab", "lineEnding": "lf", "lineWidth": 100, "expand": "auto"}, "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"noSvgWithoutTitle": "off"}, "style": {"noParameterAssign": "error", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error", "useImportType": "error"}, "suspicious": {"noExplicitAny": "warn"}}}, "javascript": {"formatter": {"trailingCommas": "all", "semicolons": "always", "bracketSameLine": false}}, "html": {"formatter": {"selfCloseVoidElements": "always"}}, "overrides": [{"includes": ["**/*.astro"], "linter": {"rules": {"style": {"useConst": "off", "useImportType": "off"}, "correctness": {"noUnusedVariables": "off", "noUnusedImports": "off"}}}}]}