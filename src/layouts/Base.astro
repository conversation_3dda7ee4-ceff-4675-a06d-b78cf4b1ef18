---
import BaseHead from "@/components/BaseHead.astro";
import Ski<PERSON>Link from "@/components/SkipLink.astro";
import ThemeProvider from "@/components/ThemeProvider.astro";
import Footer from "@/components/layout/Footer.astro";
import Header from "@/components/layout/Header.astro";
import { siteConfig } from "@/site.config";
import type { SiteMeta } from "@/types";

interface Props {
	meta: SiteMeta;
}

const {
	meta: { articleDate, description = siteConfig.description, ogImage, title },
} = Astro.props;
---

<html class="scroll-smooth" lang={siteConfig.lang}>
	<head>
		<BaseHead
			articleDate={articleDate}
			description={description}
			ogImage={ogImage}
			title={title}
		/>
	</head>
	<body
		class="bg-global-bg text-global-text mx-auto flex min-h-screen max-w-3xl flex-col px-4 pt-16 text-sm font-normal antialiased sm:px-8"
	>
		<ThemeProvider />
		<SkipLink />
		<Header />
		<main id="main">
			<slot />
		</main>
		<Footer />
		<script is:inline type="speculationrules">
			{
				"prefetch": [
					{
						"where": {
							"href_matches": "/*"
						},
						"eagerness": "immediate"
					}
				],
				"prerender": [
					{
						"where": {
							"href_matches": "/*"
						},
						"eagerness": "moderate"
					}
				]
			}
		</script>
	</body>
</html>
