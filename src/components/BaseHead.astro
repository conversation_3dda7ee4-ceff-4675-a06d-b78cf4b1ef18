---
import { WEBMENTION_PINGBACK, WEBMENTION_URL } from "astro:env/client";
import { siteConfig } from "@/site.config";
import type { SiteMeta } from "@/types";
import "@/styles/global.css";

type Props = SiteMeta;

const { articleDate, description, ogImage, title } = Astro.props;

const titleSeparator = "•";
const siteTitle = `${title} ${titleSeparator} ${siteConfig.title}`;
const canonicalURL = new URL(Astro.url.pathname, Astro.site);
const socialImageURL = new URL(
	ogImage ? ogImage : "/social-card.png",
	Astro.url,
).href;
---

<meta charset="utf-8" />
<meta content="width=device-width, initial-scale=1.0" name="viewport" />
<title>{siteTitle}</title>

{/* DM Sans Variable Font */}
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
<link
	href="https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap"
	rel="stylesheet"
/>

{/* Icons */}
<link href="/moatkon-icon.png" rel="icon" type="image/png" />
{
	import.meta.env.PROD && (
		<>
			{/* Favicon & Apple Icon */}
			<link rel="icon" href="/favicon-32x32.png" type="image/png" />
			<link href="/icons/apple-touch-icon.png" rel="apple-touch-icon" />
			{/* Manifest */}
			<link href="/manifest.webmanifest" rel="manifest" />
		</>
	)
}

{/* Canonical URL */}
<link href={canonicalURL} rel="canonical" />

{/* Primary Meta Tags */}
<meta content={siteTitle} name="title" />
<meta content={description} name="description" />
<meta content={siteConfig.author} name="author" />

{/* Open Graph / Facebook */}
<meta content={articleDate ? "article" : "website"} property="og:type" />
<meta content={title} property="og:title" />
<meta content={description} property="og:description" />
<meta content={canonicalURL} property="og:url" />
<meta content={siteConfig.title} property="og:site_name" />
<meta content={siteConfig.ogLocale} property="og:locale" />
<meta content={socialImageURL} property="og:image" />
<meta content="1200" property="og:image:width" />
<meta content="630" property="og:image:height" />
{
	articleDate && (
		<>
			<meta content={siteConfig.author} property="article:author" />
			<meta content={articleDate} property="article:published_time" />
		</>
	)
}

{/* Twitter */}
<meta content="summary_large_image" property="twitter:card" />
<meta content={canonicalURL} property="twitter:url" />
<meta content={title} property="twitter:title" />
<meta content={description} property="twitter:description" />
<meta content={socialImageURL} property="twitter:image" />

{/* Sitemap */}
<link href="/sitemap-index.xml" rel="sitemap" />

{/* RSS auto-discovery */}
<link href="/rss.xml" title="Blog" rel="alternate" type="application/rss+xml" />
<link
	href="/notes/rss.xml"
	title="Notes"
	rel="alternate"
	type="application/rss+xml"
/>

{/* Webmentions */}
{
	WEBMENTION_URL && (
		<>
			<link href={WEBMENTION_URL} rel="webmention" />
			{WEBMENTION_PINGBACK && (
				<link href={WEBMENTION_PINGBACK} rel="pingback" />
			)}
		</>
	)
}

<meta content={Astro.generator} name="generator" />
