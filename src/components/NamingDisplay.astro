---
// 动态计算当前周记名和日记名的组件
// 基于 ISO 8601 标准计算周数

interface Props {
	showWeekly?: boolean;
	showDaily?: boolean;
	className?: string;
}

const { showWeekly = true, showDaily = true, className = "" } = Astro.props;

// 这里不再进行服务端计算，改为客户端动态计算
---

<naming-display
	class={`naming-card ${className}`}
	data-show-weekly={showWeekly}
	data-show-daily={showDaily}
>
	<div class="card-header">
		<div class="card-icon">📅</div>
		<h3 class="card-title">当前命名</h3>
	</div>
	<div class="card-content">
		{
			showWeekly && (
				<div class="naming-item weekly">
					<div class="item-icon">📝</div>
					<div class="item-content">
						<span class="item-label">周记</span>
						<span class="item-value weekly-name">加载中...</span>
					</div>
				</div>
			)
		}
		{
			showDaily && (
				<div class="naming-item daily">
					<div class="item-icon">📖</div>
					<div class="item-content">
						<span class="item-label">日记</span>
						<span class="item-value daily-name">加载中...</span>
					</div>
				</div>
			)
		}
	</div>
</naming-display>

<style>
	naming-display {
		display: block;
	}

	.naming-card {
		margin-bottom: 1.5rem;
		border-radius: 0.75rem;
		background: var(--color-global-bg);
		border: 1px solid
			color-mix(in srgb, var(--color-global-text) 15%, transparent);
		box-shadow: 0 1px 3px 0
			color-mix(in srgb, var(--color-global-text) 10%, transparent);
		overflow: hidden;
		transition: all 0.2s ease;
	}

	.naming-card:hover {
		box-shadow: 0 4px 12px 0
			color-mix(in srgb, var(--color-global-text) 15%, transparent);
		transform: translateY(-1px);
	}

	.card-header {
		display: flex;
		align-items: center;
		gap: 0.75rem;
		padding: 1rem 1.25rem 0.75rem;
		background: linear-gradient(
			135deg,
			color-mix(in srgb, var(--color-accent) 8%, transparent),
			color-mix(in srgb, var(--color-accent) 4%, transparent)
		);
		border-bottom: 1px solid
			color-mix(in srgb, var(--color-global-text) 8%, transparent);
	}

	.card-icon {
		font-size: 1.25rem;
		line-height: 1;
	}

	.card-title {
		margin: 0;
		font-size: 1rem;
		font-weight: 600;
		color: var(--color-accent);
		letter-spacing: -0.025em;
	}

	.card-content {
		padding: 1rem 1.25rem 1.25rem;
	}

	.naming-item {
		display: flex;
		align-items: center;
		gap: 0.75rem;
		padding: 0.75rem;
		border-radius: 0.5rem;
		background: color-mix(
			in srgb,
			var(--color-global-text) 3%,
			transparent
		);
		transition: background-color 0.15s ease;
	}

	.naming-item:not(:last-child) {
		margin-bottom: 0.75rem;
	}

	.naming-item:hover {
		background: color-mix(
			in srgb,
			var(--color-global-text) 6%,
			transparent
		);
	}

	.item-icon {
		font-size: 1.125rem;
		line-height: 1;
		opacity: 0.8;
	}

	.item-content {
		display: flex;
		flex-direction: column;
		gap: 0.25rem;
		flex: 1;
	}

	.item-label {
		font-size: 0.75rem;
		font-weight: 500;
		color: color-mix(in srgb, var(--color-global-text) 60%, transparent);
		text-transform: uppercase;
		letter-spacing: 0.05em;
	}

	.item-value {
		font-family:
			ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
			"Liberation Mono", "Courier New", monospace;
		font-weight: 700;
		font-size: 1.125rem;
		color: var(--color-accent);
		letter-spacing: 0.025em;
	}

	/* 深色模式适配 */
	[data-theme="dark"] .naming-card {
		border-color: color-mix(
			in srgb,
			var(--color-global-text) 20%,
			transparent
		);
		box-shadow: 0 1px 3px 0
			color-mix(in srgb, var(--color-global-text) 20%, transparent);
	}

	[data-theme="dark"] .naming-card:hover {
		box-shadow: 0 4px 12px 0
			color-mix(in srgb, var(--color-global-text) 25%, transparent);
	}

	[data-theme="dark"] .card-header {
		background: linear-gradient(
			135deg,
			color-mix(in srgb, var(--color-accent) 12%, transparent),
			color-mix(in srgb, var(--color-accent) 6%, transparent)
		);
		border-bottom-color: color-mix(
			in srgb,
			var(--color-global-text) 15%,
			transparent
		);
	}

	[data-theme="dark"] .naming-item {
		background: color-mix(
			in srgb,
			var(--color-global-text) 6%,
			transparent
		);
	}

	[data-theme="dark"] .naming-item:hover {
		background: color-mix(
			in srgb,
			var(--color-global-text) 10%,
			transparent
		);
	}

	/* 响应式设计 */
	@media (min-width: 640px) {
		.card-content {
			display: flex;
			gap: 1rem;
		}

		.naming-item {
			flex: 1;
			margin-bottom: 0;
		}

		.naming-item:not(:last-child) {
			margin-bottom: 0;
		}
	}
</style>

<script>
	// 客户端动态计算周数和日期
	class NamingDisplay extends HTMLElement {
		connectedCallback() {
			this.updateNaming();
		}

		// 修正的 ISO 8601 周数计算，与Google日历匹配
		getISOWeek(date: Date): {
			year: number;
			week: number;
			dayOfWeek: number;
		} {
			const target = new Date(date.valueOf());

			// 获取星期几 (0=周日, 1=周一, ..., 6=周六)
			const dayOfWeek = target.getDay();
			// 转换为 ISO 格式 (1=周一, 2=周二, ..., 7=周日)
			const isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;

			// 复制日期并调整到本周的周四
			const thursday = new Date(target);
			thursday.setDate(target.getDate() + (4 - isoDayOfWeek));

			// 获取周四所在的年份
			const year = thursday.getFullYear();

			// 计算该年1月4日（ISO 8601标准中第1周总是包含1月4日）
			const jan4 = new Date(year, 0, 4);

			// 找到1月4日所在周的周一
			const jan4DayOfWeek = jan4.getDay();
			const jan4Monday = new Date(jan4);
			jan4Monday.setDate(
				4 - (jan4DayOfWeek === 0 ? 7 : jan4DayOfWeek) + 1,
			);

			// 计算周数
			const weekNumber =
				Math.floor(
					(thursday.getTime() - jan4Monday.getTime()) /
						(7 * 24 * 60 * 60 * 1000),
				) + 1;

			return {
				year: year,
				week: weekNumber,
				dayOfWeek: isoDayOfWeek,
			};
		}

		updateNaming() {
			const now = new Date();
			const { year, week, dayOfWeek } = this.getISOWeek(now);

			const weeklyName = `${year} W${week}`;
			const dailyName = `${year} W${week} D${dayOfWeek}`;

			// 更新显示
			const weeklyElement = this.querySelector(".weekly-name");
			const dailyElement = this.querySelector(".daily-name");

			if (weeklyElement) {
				weeklyElement.textContent = weeklyName;
			}

			if (dailyElement) {
				dailyElement.textContent = dailyName;
			}
		}
	}

	customElements.define("naming-display", NamingDisplay);
</script>
