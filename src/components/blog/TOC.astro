---
import { generateToc } from "@/utils/generateToc";
import type { MarkdownHeading } from "astro";
import TOCHeading from "./TOCHeading.astro";

interface Props {
	headings: MarkdownHeading[];
}

const { headings } = Astro.props;

const toc = generateToc(headings);
---

<details open class="lg:sticky lg:top-12 lg:order-2 lg:-me-32 lg:basis-64">
	<summary class="title hover:marker:text-accent cursor-pointer text-lg">目录</summary>
	<nav class="ms-4 lg:w-full">
		<ol class="mt-4">
			{toc.map((heading) => <TOCHeading heading={heading} />)}
		</ol>
	</nav>
</details>
